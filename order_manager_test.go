package main

import (
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"
)

// Мок функция для cancelOrder в тестах
func init() {
	// Переопределяем cancelOrder для тестов
	originalCancelOrder := cancelOrder
	cancelOrder = func(orderID string) error {
		// Имитируем успешное снятие ордера
		return nil
	}
	_ = originalCancelOrder // избегаем предупреждения о неиспользуемой переменной
}

// Тест производительности OrderManager для HFT
func TestOrderManagerPerformance(t *testing.T) {
	om := NewOrderManager()
	defer om.Stop()

	// Тест латентности добавления ордеров
	numOrders := 1000
	start := time.Now()

	for i := 0; i < numOrders; i++ {
		order := OrderInfo{
			OrderID:   fmt.Sprintf("order_%d", i),
			Price:     100.0 + float64(i)*0.01,
			Side:      "Buy",
			Timestamp: time.Now(),
		}
		om.AddOrder(order)
	}

	// Ждем обработки всех команд
	time.Sleep(10 * time.Millisecond)
	
	addDuration := time.Since(start)
	avgAddLatency := addDuration / time.Duration(numOrders)

	fmt.Printf("Добавление %d ордеров заняло: %v\n", numOrders, addDuration)
	fmt.Printf("Средняя латентность добавления: %v\n", avgAddLatency)

	// Проверяем количество ордеров
	count := om.GetOrderCount()
	if count != numOrders {
		t.Errorf("Ожидалось %d ордеров, получено %d", numOrders, count)
	}

	// Тест латентности удаления ордеров
	start = time.Now()
	for i := 0; i < numOrders/2; i++ {
		om.RemoveOrder(fmt.Sprintf("order_%d", i))
	}

	// Ждем обработки всех команд
	time.Sleep(10 * time.Millisecond)
	
	removeDuration := time.Since(start)
	avgRemoveLatency := removeDuration / time.Duration(numOrders/2)

	fmt.Printf("Удаление %d ордеров заняло: %v\n", numOrders/2, removeDuration)
	fmt.Printf("Средняя латентность удаления: %v\n", avgRemoveLatency)

	// Проверяем количество оставшихся ордеров
	finalCount := om.GetOrderCount()
	expectedCount := numOrders - numOrders/2
	if finalCount != expectedCount {
		t.Errorf("Ожидалось %d ордеров, получено %d", expectedCount, finalCount)
	}

	// Проверяем, что латентность приемлема для HFT (< 100 микросекунд)
	if avgAddLatency > 100*time.Microsecond {
		t.Errorf("Латентность добавления слишком высокая: %v", avgAddLatency)
	}
	if avgRemoveLatency > 100*time.Microsecond {
		t.Errorf("Латентность удаления слишком высокая: %v", avgRemoveLatency)
	}
}

// Тест конкурентного доступа (имитация race condition)
func TestOrderManagerConcurrency(t *testing.T) {
	om := NewOrderManager()
	defer om.Stop()

	var wg sync.WaitGroup
	numGoroutines := 10
	ordersPerGoroutine := 100

	// Запускаем несколько горутин, добавляющих ордера одновременно
	for g := 0; g < numGoroutines; g++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			for i := 0; i < ordersPerGoroutine; i++ {
				order := OrderInfo{
					OrderID:   fmt.Sprintf("g%d_order_%d", goroutineID, i),
					Price:     100.0 + float64(i)*0.01,
					Side:      "Buy",
					Timestamp: time.Now(),
				}
				om.AddOrder(order)
			}
		}(g)
	}

	wg.Wait()
	time.Sleep(50 * time.Millisecond) // Ждем обработки всех команд

	expectedTotal := numGoroutines * ordersPerGoroutine
	actualCount := om.GetOrderCount()

	if actualCount != expectedTotal {
		t.Errorf("Ожидалось %d ордеров, получено %d", expectedTotal, actualCount)
	}

	fmt.Printf("Конкурентный тест: %d горутин добавили %d ордеров каждая\n", numGoroutines, ordersPerGoroutine)
	fmt.Printf("Итого ордеров: %d\n", actualCount)
}

// Тест очистки старых ордеров
func TestOrderManagerCleanup(t *testing.T) {
	// Временно изменяем настройки для теста
	originalCancelMode := cancelOldOrders
	originalTimeout := cancelTimeoutSeconds
	
	cancelOldOrders = "remove_after_timeout"
	cancelTimeoutSeconds = 0.1 // 100ms

	defer func() {
		cancelOldOrders = originalCancelMode
		cancelTimeoutSeconds = originalTimeout
	}()

	om := NewOrderManager()
	defer om.Stop()

	// Добавляем несколько ордеров
	for i := 0; i < 5; i++ {
		order := OrderInfo{
			OrderID:   fmt.Sprintf("test_order_%d", i),
			Price:     100.0,
			Side:      "Buy",
			Timestamp: time.Now(),
		}
		om.AddOrder(order)
	}

	time.Sleep(10 * time.Millisecond)
	initialCount := om.GetOrderCount()
	fmt.Printf("Начальное количество ордеров: %d\n", initialCount)

	// Ждем, пока ордера устареют
	time.Sleep(150 * time.Millisecond)

	// Запускаем очистку
	om.CleanupOrders()
	time.Sleep(10 * time.Millisecond)

	finalCount := om.GetOrderCount()
	fmt.Printf("Количество ордеров после очистки: %d\n", finalCount)

	// В реальном тесте ордера не будут удалены, так как cancelOrder не реализован для тестов
	// Но мы проверяем, что система не падает
	if finalCount < 0 {
		t.Errorf("Некорректное количество ордеров после очистки: %d", finalCount)
	}
}
